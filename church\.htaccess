AddType application/x-httpd-php .php

<IfModule mod_php.c>
    php_flag display_errors off
    php_value upload_max_filesize 5M
    php_value post_max_size 6M
    php_flag session.auto_start off
</IfModule>

<IfModule mod_rewrite.c>
    RewriteEngine On
    
    # Set RewriteBase dynamically - auto-detect subdirectory
    # RewriteBase will be auto-detected by Apache based on the directory structure
    
    # Allow direct access to actual files and directories
    RewriteCond %{REQUEST_FILENAME} -f [OR]
    RewriteCond %{REQUEST_FILENAME} -d
    RewriteRule ^ - [L]
    
    # Handle admin routes first
    RewriteRule ^admin/(.*)$ admin/$1 [QSA,L]

    # Handle user routes - redirect user pages to user directory
    RewriteRule ^(requests|my_gifts|dashboard|profile|settings|events|birthday_templates|family_management|send_gift|change_password|skills|volunteer_opportunities)\.php$ user/$1.php [QSA,L]

    # Handle all other routes through register.php (only for non-existent files)
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} !^/church/user/
    RewriteRule ^(.*)$ register.php?url=$1 [QSA,L]
</IfModule>

# Handle errors - use relative paths
ErrorDocument 400 400.php
ErrorDocument 401 401.php
ErrorDocument 403 403.php
ErrorDocument 404 404.php
ErrorDocument 500 500.php

# Security headers
<IfModule mod_headers.c>
    Header set X-Content-Type-Options "nosniff"
    Header set X-Frame-Options "SAMEORIGIN"
    Header set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
    # Add Content Security Policy
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com; img-src 'self' data: https:; font-src 'self' data: https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.gstatic.com; connect-src 'self';"
</IfModule>

# Prevent directory listing
Options -Indexes

# Protect sensitive files
<FilesMatch "^\.">
    Order allow,deny
    Deny from all
</FilesMatch>

<FilesMatch "(composer\.json|composer\.lock|package\.json|package-lock\.json|config\.php|environment\.php|\.env)$">
    Order allow,deny
    Deny from all
</FilesMatch>

# PHP settings - safe settings only
<IfModule mod_php.c>
    # Set maximum execution time
    php_value max_execution_time 60
    # Set maximum memory limit
    php_value memory_limit 128M
</IfModule>
